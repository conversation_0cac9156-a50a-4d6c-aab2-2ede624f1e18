import { Injectable, Logger } from '@nestjs/common';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { ConfigService, ConfigType, StorageConfig } from '@config';
import * as crypto from 'crypto';
import { TimeIntervalEnum } from '../utils';

@Injectable()
export class CdnService {
  private readonly logger = new Logger(CdnService.name);
  private readonly cdnUrl: string;
  private readonly secretKey: string;

  constructor(private readonly configService: ConfigService) {
    const storageConfig = this.configService.getConfig<StorageConfig>(ConfigType.Storage);
    this.cdnUrl = storageConfig.cdn.url;
    this.secretKey = storageConfig.cdn.secretKey;
  }

  /**
   * Tạo URL có chữ ký để xem tài nguyên từ CDN
   * @param key Đường dẫn tài nguyên trên CDN
   * @param time Thờ<PERSON> gian hết hạn
   * @returns URL có chữ ký
   */
  generateUrlView(key: string, time: TimeIntervalEnum): string | null {
    if (!key || key.length === 0) {
      return null;
    }

    try {
      const unixTime = Math.floor(Date.now() / 1000);
      const expires = unixTime + time;
      const text = `${expires}|${key}`;

      // Tạo chữ ký HMAC-SHA1
      const hmac = crypto.createHmac('sha1', this.secretKey);
      hmac.update(text);
      const signature = hmac.digest('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');

      // Trả về URL với chữ ký và thời gian hết hạn
      return `${this.cdnUrl}/${key}?expires=${expires}&signature=${signature}`;
    } catch (error) {
      this.logger.error(`Error generating signed URL: ${error.message}`);
      throw new AppException(ErrorCode.CDN_URL_GENERATION_ERROR, error.message);
    }
  }
}
